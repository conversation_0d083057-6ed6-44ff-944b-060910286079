#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Author: 汪祥
# @Time : 2025/08/04 18:29:53
# @File : 2025-08-04_<PERSON><PERSON>_TCN_xLSTM_KnowledgeReasoning.py
# @Note : 卡尔曼滤波、多物理量、TCN、XLSTM、知识推理
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy import stats
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import seaborn as sns
from matplotlib import font_manager, rcParams
from scipy.signal import stft, welch
import time
import os


def create_plot_directories():
    """创建用于存储不同模块图表的文件夹"""
    directories = {
        'tcn': 'tcn_analysis_plots',
        'xlstm': 'xlstm_analysis_plots',
        'knowledge': 'knowledge_reasoning_plots'
    }
    
    for dir_name in directories.values():
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
    
    return directories


# 设置中文字体
font_path = '/System/Library/Fonts/Hiragino Sans GB.ttc'
font_prop = font_manager.FontProperties(fname=font_path, size=12)
rcParams['font.family'] = font_prop.get_name()
rcParams['axes.unicode_minus'] = False

# 设置随机种子保证可复现性
np.random.seed(42)
torch.manual_seed(42)

# 1. 改进的卡尔曼滤波器实现 ----------------------------------------------------------
class AdaptiveKalmanFilter:
    def __init__(self, initial_variance=1.0, min_process_variance=1e-6, max_process_variance=1e-2, 
                 min_measurement_variance=1e-4, max_measurement_variance=1.0, innovation_threshold=3.0):
        """
        改进的自适应卡尔曼滤波器
        :param initial_variance: 初始方差估计
        :param min_process_variance: 最小过程噪声方差
        :param max_process_variance: 最大过程噪声方差
        :param min_measurement_variance: 最小测量噪声方差
        :param max_measurement_variance: 最大测量噪声方差
        :param innovation_threshold: 创新值异常检测阈值
        """
        self.min_process_variance = min_process_variance
        self.max_process_variance = max_process_variance
        self.min_measurement_variance = min_measurement_variance
        self.max_measurement_variance = max_measurement_variance
        self.innovation_threshold = innovation_threshold
        
        # 初始状态
        self.posteri_estimate = 0.0
        self.posteri_error_estimate = initial_variance
        self.innovation_history = []
        self.measurement_variance_history = []
        self.process_variance_history = []
        
    def adaptive_variance(self, innovation, prev_measurement_var):
        """
        自适应调整噪声方差
        """
        # 基于创新值调整测量噪声方差
        abs_innovation = np.abs(innovation)
        
        # 动态调整测量噪声
        if abs_innovation > self.innovation_threshold:
            # 创新值过大，增加测量噪声（降低对当前测量的信任）
            measurement_variance = min(prev_measurement_var * 1.5, self.max_measurement_variance)
        else:
            # 创新值正常，缓慢减小测量噪声
            measurement_variance = max(prev_measurement_var * 0.95, self.min_measurement_variance)
        
        # 过程噪声与测量噪声保持比例关系
        process_variance = max(min(measurement_variance * 0.01, self.max_process_variance), self.min_process_variance)
        
        return process_variance, measurement_variance
    
    def update(self, measurement, prev_measurement_var):
        """
        更新卡尔曼滤波器状态
        :param measurement: 当前测量值
        :param prev_measurement_var: 上一时刻的测量噪声方差
        :return: (滤波后的值, 卡尔曼增益, 过程噪声方差, 测量噪声方差)
        """
        # 预测步骤
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate
        
        # 计算创新值（测量残差）
        innovation = measurement - priori_estimate
        
        # 自适应调整噪声参数
        process_variance, measurement_variance = self.adaptive_variance(innovation, prev_measurement_var)
        
        # 更新步骤
        kalman_gain = priori_error_estimate / (priori_error_estimate + measurement_variance)
        self.posteri_estimate = priori_estimate + kalman_gain * innovation
        self.posteri_error_estimate = (1 - kalman_gain) * priori_error_estimate + process_variance
        
        # 保存历史记录
        self.innovation_history.append(innovation)
        self.measurement_variance_history.append(measurement_variance)
        self.process_variance_history.append(process_variance)
        
        return self.posteri_estimate, kalman_gain, process_variance, measurement_variance

def apply_adaptive_kalman_filter(signal, initial_measurement_var=0.1, innovation_threshold=3.0):
    """
    应用自适应卡尔曼滤波，并返回滤波信号和状态特征
    """
    kf = AdaptiveKalmanFilter(initial_variance=1.0, innovation_threshold=innovation_threshold)
    filtered_signal = np.zeros_like(signal)
    kalman_gains = np.zeros_like(signal)
    process_variances = np.zeros_like(signal)
    measurement_variances = np.zeros_like(signal)
    
    # 使用初始测量方差
    measurement_var = initial_measurement_var
    
    for i, value in enumerate(signal):
        filtered_value, gain, proc_var, meas_var = kf.update(value, measurement_var)
        filtered_signal[i] = filtered_value
        kalman_gains[i] = gain
        process_variances[i] = proc_var
        measurement_variances[i] = meas_var
        measurement_var = meas_var  # 为下一步更新
    
    # 返回滤波信号和状态特征
    state_features = np.column_stack((kalman_gains, process_variances, measurement_variances))
    return filtered_signal, state_features

# 2. 改进的特征工程函数 -----------------------------------------------------------
def extract_enhanced_frequency_features(signal, window_size=256, nperseg=64):
    """
    提取增强的频域特征，包括功率谱特征
    """
    # 确保信号长度足够
    if len(signal) < window_size:
        window_size = len(signal) // 2
    
    # 初始化特征数组
    freq_features = np.zeros((len(signal), 5))  # 均值、标准差、最大幅值、主要频率、带宽
    
    # 滑动窗口提取频域特征
    for i in range(len(signal) - window_size + 1):
        segment = signal[i:i+window_size]
        
        # 计算STFT
        f, t, Zxx = stft(segment, nperseg=nperseg, boundary='even')
        magnitude = np.abs(Zxx)
        
        # 计算功率谱
        freqs, psd = welch(segment, fs=1000, nperseg=64)
        
        # 计算频域特征
        freq_features[i+window_size-1, 0] = np.mean(magnitude)  # 平均幅值
        freq_features[i+window_size-1, 1] = np.std(magnitude)   # 幅值标准差
        freq_features[i+window_size-1, 2] = np.max(magnitude)   # 最大幅值
        
        # 功率谱特征
        if len(psd) > 0:
            freq_features[i+window_size-1, 3] = freqs[np.argmax(psd)]  # 主要频率
            freq_features[i+window_size-1, 4] = np.sum(psd > np.max(psd)*0.5) / len(psd)  # 带宽占比
    
    # 处理开头部分
    for i in range(window_size - 1):
        freq_features[i] = freq_features[window_size - 1]
    
    return freq_features

# 3. TCN模块实现 -----------------------------------------------------------------
class TCNBlock(nn.Module):
    """时间卷积网络基本块，包含膨胀卷积和残差连接"""
    def __init__(self, in_channels, out_channels, kernel_size, dilation, dropout=0.2):
        super(TCNBlock, self).__init__()
        # 修正padding计算，确保输出长度与输入相同
        padding = (kernel_size - 1) * dilation // 2
        
        self.conv1 = nn.Conv1d(
            in_channels, out_channels, kernel_size, 
            padding=padding,
            dilation=dilation
        )
        self.norm1 = nn.BatchNorm1d(out_channels)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(
            out_channels, out_channels, kernel_size, 
            padding=padding, 
            dilation=dilation
        )
        self.norm2 = nn.BatchNorm1d(out_channels)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        # 下采样卷积（当输入输出通道数不同时）
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        
    def forward(self, x):
        residual = x
        
        # 第一层卷积
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        # 第二层卷积
        out = self.conv2(out)
        out = self.norm2(out)
        
        # 确保残差连接的尺寸匹配
        if self.downsample is not None:
            residual = self.downsample(residual)
            
        # 如果尺寸仍不匹配，裁剪到较小的尺寸
        if out.size(2) != residual.size(2):
            min_size = min(out.size(2), residual.size(2))
            out = out[:, :, :min_size]
            residual = residual[:, :, :min_size]
        
        out += residual
        
        out = self.relu2(out)
        out = self.dropout2(out)
        return out

class TCNModule(nn.Module):
    """完整的TCN模块，包含多个膨胀卷积块"""
    def __init__(self, input_size, tcn_channels, kernel_size, num_blocks, dropout=0.2):
        super(TCNModule, self).__init__()
        self.layers = nn.ModuleList()
        
        # 输入投影层，将特征维度映射到TCN通道数
        self.input_proj = nn.Conv1d(input_size, tcn_channels, 1)
        
        # 创建多个TCN块，膨胀率指数增长
        for i in range(num_blocks):
            dilation = 2 ** i  # 膨胀率指数增长
            self.layers.append(
                TCNBlock(tcn_channels, tcn_channels, kernel_size, dilation, dropout)
            )
            
    def forward(self, x):
        # 输入形状: (batch, seq_len, features) -> 转置为 (batch, features, seq_len)
        x = x.permute(0, 2, 1)
        
        # 输入投影
        x = self.input_proj(x)
        
        # 通过所有TCN块
        for layer in self.layers:
            x = layer(x)
            
        # 转回原始形状: (batch, seq_len, features)
        return x.permute(0, 2, 1)

# 4. xLSTM模型定义（与TCN融合）-----------------------------------------------------
class xLSTMCell(nn.Module):
    def __init__(self, input_size, hidden_size):
        super(xLSTMCell, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # 输入门、遗忘门、输出门和候选值的线性变换
        self.W_i = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_f = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_o = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_g = nn.Linear(input_size + hidden_size, hidden_size)
        
        # xLSTM的改进：添加归一化层
        self.layer_norm_i = nn.LayerNorm(hidden_size)
        self.layer_norm_f = nn.LayerNorm(hidden_size)
        self.layer_norm_o = nn.LayerNorm(hidden_size)
        self.layer_norm_g = nn.LayerNorm(hidden_size)
        
    def forward(self, x, hidden):
        h_prev, c_prev = hidden
        
        # 拼接输入和前一时刻的隐藏状态
        combined = torch.cat([x, h_prev], dim=1)
        
        # 计算门控值（添加层归一化）
        i_t = torch.sigmoid(self.layer_norm_i(self.W_i(combined)))
        f_t = torch.sigmoid(self.layer_norm_f(self.W_f(combined)))
        o_t = torch.sigmoid(self.layer_norm_o(self.W_o(combined)))
        g_t = torch.tanh(self.layer_norm_g(self.W_g(combined)))
        
        # 更新细胞状态和隐藏状态
        c_t = f_t * c_prev + i_t * g_t
        h_t = o_t * torch.tanh(c_t)
        
        return h_t, c_t

class TCN_xLSTM_Fusion(nn.Module):
    def __init__(self, input_size, tcn_channels, tcn_kernel_size, tcn_blocks, 
                 hidden_size, num_layers, output_size, dropout=0.2):
        super(TCN_xLSTM_Fusion, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # TCN模块
        self.tcn = TCNModule(
            input_size, 
            tcn_channels, 
            kernel_size=tcn_kernel_size,
            num_blocks=tcn_blocks,
            dropout=dropout
        )
        
        # 多层xLSTM
        self.xlstm_layers = nn.ModuleList([
            xLSTMCell(tcn_channels if i == 0 else hidden_size, hidden_size)
            for i in range(num_layers)
        ])
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 全连接层
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, hidden_size // 4)
        self.fc3 = nn.Linear(hidden_size // 4, output_size)
        self.relu = nn.ReLU()
        
        # 特征交互层
        self.interaction_fc = nn.Linear(hidden_size, hidden_size)
        
        # 输出层初始化
        self._init_weights()
        
    def _init_weights(self):
        """权重初始化"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'fc' in name or 'W_' in name:
                    nn.init.xavier_normal_(param)
                elif 'conv' in name:
                    nn.init.kaiming_normal_(param, mode='fan_out', nonlinearity='relu')
            elif 'bias' in name:
                nn.init.constant_(param, 0.0)
                
    def forward(self, x):
        batch_size, seq_len, feature_size = x.size()
        
        # 通过TCN模块提取局部特征
        tcn_out = self.tcn(x)
        
        # 初始化隐藏状态
        h = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        c = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        
        # 逐时间步处理TCN输出
        for t in range(seq_len):
            x_t = tcn_out[:, t, :]
            for layer in range(self.num_layers):
                if layer == 0:
                    h[layer], c[layer] = self.xlstm_layers[layer](x_t, (h[layer], c[layer]))
                else:
                    # 特征交互增强
                    if layer == self.num_layers - 1:
                        interactive = self.relu(self.interaction_fc(h[layer-1]))
                        h[layer], c[layer] = self.xlstm_layers[layer](interactive, (h[layer], c[layer]))
                    else:
                        h[layer], c[layer] = self.xlstm_layers[layer](h[layer-1], (h[layer], c[layer]))
        
        # 使用最后一层的最后时刻输出
        output = self.dropout(h[-1])
        output = self.relu(self.fc1(output))
        output = self.relu(self.fc2(output))
        output = self.fc3(output)
        
        return output

# 5. 改进的数据准备 -----------------------------------------------------------
# 参数配置
TIME_STEPS = 120
TEST_SIZE = 0.40
VAL_SIZE = 0.15
BATCH_SIZE = 32
EPOCHS = 100
LEARNING_RATE = 0.001
FREQ_WINDOW_SIZE = 128  # 频域特征提取窗口大小

# TCN参数
TCN_CHANNELS = 64     # TCN输出通道数
TCN_KERNEL_SIZE = 5   # TCN卷积核大小
TCN_BLOCKS = 4        # TCN块数量

# 改进的加载和融合数据函数
def load_and_fuse_data(tension_path, vibration_path, extract_freq_features=True, apply_kalman=True):
    # 读取张力数据
    tension_df = pd.read_csv(tension_path)
    tension_df = tension_df[['Tension']].rename(columns={'Tension': 'tension'})
    
    # 读取振动数据
    vibration_df = pd.read_csv(vibration_path)
    vibration_df = vibration_df[['WT1_Z']].rename(columns={'WT1_Z': 'vibration'})
    
    # 确保两个数据集长度相同
    min_length = min(len(tension_df), len(vibration_df))
    tension_df = tension_df.iloc[:min_length]
    vibration_df = vibration_df.iloc[:min_length]
    
    # 保存原始数据
    fused_df = pd.DataFrame({
        'raw_tension': tension_df['tension'].values,
        'raw_vibration': vibration_df['vibration'].values
    })
    
    # 应用自适应卡尔曼滤波
    if apply_kalman:
        print("应用自适应卡尔曼滤波...")
        
        # 对张力信号应用自适应卡尔曼滤波
        tension_filtered, tension_state_features = apply_adaptive_kalman_filter(
            tension_df['tension'].values, 
            initial_measurement_var=0.1,
            innovation_threshold=1.0
        )
        
        # 对振动信号应用自适应卡尔曼滤波
        vibration_filtered, vibration_state_features = apply_adaptive_kalman_filter(
            vibration_df['vibration'].values, 
            initial_measurement_var=0.5,
            innovation_threshold=1.0
        )
        
        # 添加滤波后信号和状态特征
        fused_df['tension'] = tension_filtered
        fused_df['tension_kalman_gain'] = tension_state_features[:, 0]
        fused_df['tension_process_var'] = tension_state_features[:, 1]
        fused_df['tension_measure_var'] = tension_state_features[:, 2]
        
        fused_df['vibration'] = vibration_filtered
        fused_df['vibration_kalman_gain'] = vibration_state_features[:, 0]
        fused_df['vibration_process_var'] = vibration_state_features[:, 1]
        fused_df['vibration_measure_var'] = vibration_state_features[:, 2]
    else:
        # 如果不使用卡尔曼滤波，直接使用原始信号
        fused_df['tension'] = fused_df['raw_tension']
        fused_df['vibration'] = fused_df['raw_vibration']
    
    # 添加特征交互项（使用滤波后信号）
    fused_df['interaction'] = fused_df['tension'] * fused_df['vibration']
    
    # 提取振动频域特征（使用滤波后信号）
    if extract_freq_features:
        print("提取增强的振动频域特征...")
        vibration_signal = fused_df['vibration'].values
        freq_features = extract_enhanced_frequency_features(vibration_signal, window_size=FREQ_WINDOW_SIZE)
        
        # 添加频域特征
        fused_df['freq_mean'] = freq_features[:, 0]
        fused_df['freq_std'] = freq_features[:, 1]
        fused_df['freq_max'] = freq_features[:, 2]
        fused_df['freq_dominant'] = freq_features[:, 3]
        fused_df['freq_bandwidth'] = freq_features[:, 4]
    
    return fused_df

# 加载融合数据
print("加载和融合数据...")
fused_df = load_and_fuse_data('725full.csv', '725full.csv', extract_freq_features=True, apply_kalman=True)

# 特征列表（包含卡尔曼状态特征）
features = [
    'tension', 'vibration', 'interaction',
    'tension_kalman_gain', 'tension_process_var', 'tension_measure_var',
    'vibration_kalman_gain', 'vibration_process_var', 'vibration_measure_var',
    'freq_mean', 'freq_std', 'freq_max', 'freq_dominant', 'freq_bandwidth'
]
print(f"使用的特征 ({len(features)}个): {features}")

# 保存卡尔曼滤波后的14个特征数据并可视化 -----------------------------------------------------------
def save_and_plot_kalman_features(fused_df, features):
    """
    保存卡尔曼滤波后的14个特征数据到CSV文件并绘制曲线图
    """
    # 创建包含所有14个特征的DataFrame
    kalman_features_df = fused_df[features].copy()
    
    # 添加时间索引
    kalman_features_df.insert(0, 'TimeStep', range(len(kalman_features_df)))
    
    # 保存到CSV文件
    csv_filename = 'kalman_filtered_14_features.csv'
    kalman_features_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
    print(f"卡尔曼滤波后的14个特征数据已保存到: {csv_filename}")
    print(f"数据形状: {kalman_features_df.shape}")
    print(f"特征列表: {list(kalman_features_df.columns[1:])}")  # 排除TimeStep列
    
    # 特征信息配置
    feature_info = {
        'tension': {'color': 'blue', 'name': '张力信号', 'unit': 'KN'},
        'vibration': {'color': 'green', 'name': '振动信号', 'unit': 'G'},
        'interaction': {'color': 'purple', 'name': '交互特征', 'unit': ''},
        'tension_kalman_gain': {'color': 'orange', 'name': '张力卡尔曼增益', 'unit': ''},
        'tension_process_var': {'color': 'red', 'name': '张力过程噪声方差', 'unit': ''},
        'tension_measure_var': {'color': 'brown', 'name': '张力测量噪声方差', 'unit': ''},
        'vibration_kalman_gain': {'color': 'cyan', 'name': '振动卡尔曼增益', 'unit': ''},
        'vibration_process_var': {'color': 'magenta', 'name': '振动过程噪声方差', 'unit': ''},
        'vibration_measure_var': {'color': 'gold', 'name': '振动测量噪声方差', 'unit': ''},
        'freq_mean': {'color': 'pink', 'name': '频域平均幅值', 'unit': ''},
        'freq_std': {'color': 'gray', 'name': '频域幅值标准差', 'unit': ''},
        'freq_max': {'color': 'olive', 'name': '频域最大幅值', 'unit': ''},
        'freq_dominant': {'color': 'navy', 'name': '主要频率', 'unit': 'Hz'},
        'freq_bandwidth': {'color': 'lime', 'name': '频域带宽占比', 'unit': ''}
    }
    
    # 创建保存图片的文件夹
    import os
    output_dir = 'kalman_features_plots'
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 为每个特征创建单独的图
    for feature in features:
        plt.figure(figsize=(12.8, 7.0))  # 1280x700 像素
        
        info = feature_info.get(feature, {'color': 'black', 'name': feature, 'unit': ''})
        
        # 绘制特征曲线
        plt.plot(kalman_features_df[feature].values, color='darkblue', linewidth=2, alpha=0.8)
        
        # 设置图表格式
        plt.title(f'卡尔曼滤波后的{info["name"]}特征曲线', fontsize=14, fontweight='bold')
        plt.grid(True, linestyle='--', alpha=0.3)
        plt.tick_params(axis='both', which='major', labelsize=10)
        
        # 设置x轴和y轴标签
        plt.xlabel('时间序列', fontsize=12)
        plt.ylabel(f'{info["name"]} {info["unit"]}', fontsize=12)
        
        # 设置x轴刻度
        plt.xticks(range(0, len(kalman_features_df), max(1, len(kalman_features_df)//5)))
        
        # 添加网格
        plt.grid(True, linestyle='--', alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片到指定文件夹
        save_path = os.path.join(output_dir, f'kalman_features_{feature}.png')
        plt.savefig(save_path, dpi=100, bbox_inches='tight')
        plt.show()  # 实时预览
        plt.close()  # 关闭图表释放内存
    
    print("已生成所有14个特征的单独曲线图")
    
    return kalman_features_df

# 保存和绘制卡尔曼滤波后的14个特征数据
print("保存卡尔曼滤波后的14个特征数据并绘制曲线图...")
kalman_features_data = save_and_plot_kalman_features(fused_df, features)

# 只保留关键的数据处理，移除原始数据对比可视化

# 数据归一化 - 使用RobustScaler减少异常值影响
print("数据归一化处理...")
scalers = {}
scaled_features = []

for feature in features:
    # 对卡尔曼状态特征使用RobustScaler
    if 'kalman' in feature or 'var' in feature:
        scaler = RobustScaler()
    else:
        scaler = MinMaxScaler(feature_range=(-1, 1))
    
    scaled_feature = scaler.fit_transform(fused_df[[feature]].values)
    scalers[feature] = scaler
    scaled_features.append(scaled_feature)
    

# 创建特征矩阵
scaled_data = np.concatenate(scaled_features, axis=1)

# 详细分析归一化后的数据
def analyze_normalized_data(scaled_data, features, scalers):
    """
    详细分析归一化后传入TCN的数据
    """
    print("\n" + "="*80)
    print("归一化后传入TCN的数据分析")
    print("="*80)
    
    print(f"数据形状: {scaled_data.shape}")
    print(f"数据类型: {scaled_data.dtype}")
    print(f"总样本数: {scaled_data.shape[0]}")
    print(f"特征维度: {scaled_data.shape[1]}")
    
    print(f"\n14个特征详细信息:")
    print("-" * 80)
    print(f"{'序号':<4} {'特征名':<25} {'归一化方法':<15} {'数值范围':<20} {'均值':<10} {'标准差':<10}")
    print("-" * 80)
    
    for i, feature in enumerate(features):
        feature_data = scaled_data[:, i]
        scaler_type = type(scalers[feature]).__name__
        data_min = np.min(feature_data)
        data_max = np.max(feature_data)
        data_mean = np.mean(feature_data)
        data_std = np.std(feature_data)
        
        print(f"{i+1:<4} {feature:<25} {scaler_type:<15} [{data_min:>6.3f}, {data_max:>6.3f}] {data_mean:>8.3f} {data_std:>8.3f}")
    
    print("\n归一化方法说明:")
    print("- MinMaxScaler(-1,1): 将数据缩放到[-1, 1]区间")
    print("- RobustScaler: 使用中位数和四分位距进行缩放，对异常值更鲁棒")
    
    # 显示前5个样本的数据
    print(f"\n前5个样本的归一化数据:")
    print("-" * 120)
    header = "样本  " + "  ".join([f"{feat[:8]:<8}" for feat in features])
    print(header)
    print("-" * 120)
    
    for i in range(min(5, scaled_data.shape[0])):
        sample_str = f"{i+1:<4}  "
        for j in range(len(features)):
            sample_str += f"{scaled_data[i, j]:>8.3f}  "
        print(sample_str)
    
    # 分析不同类型特征的分布
    print(f"\n特征分组统计:")
    print("-" * 60)
    
    # 基础信号特征
    basic_features = ['tension', 'vibration', 'interaction']
    basic_indices = [features.index(f) for f in basic_features if f in features]
    if basic_indices:
        basic_data = scaled_data[:, basic_indices]
        print(f"基础信号特征 (3个): 均值={np.mean(basic_data):.3f}, 标准差={np.std(basic_data):.3f}")
    
    # 卡尔曼状态特征
    kalman_features = [f for f in features if 'kalman' in f or 'var' in f]
    kalman_indices = [features.index(f) for f in kalman_features]
    if kalman_indices:
        kalman_data = scaled_data[:, kalman_indices]
        print(f"卡尔曼状态特征 (6个): 均值={np.mean(kalman_data):.3f}, 标准差={np.std(kalman_data):.3f}")
    
    # 频域特征
    freq_features = [f for f in features if 'freq' in f]
    freq_indices = [features.index(f) for f in freq_features]
    if freq_indices:
        freq_data = scaled_data[:, freq_indices]
        print(f"频域特征 (5个): 均值={np.mean(freq_data):.3f}, 标准差={np.std(freq_data):.3f}")
    
    print("="*80)
    
    return scaled_data

# 分析归一化后的数据
scaled_data = analyze_normalized_data(scaled_data, features, scalers)

# 创建监督学习数据集
def create_dataset(data, time_steps=1):
    X, y = [], []
    for i in range(len(data) - time_steps):
        X.append(data[i:(i + time_steps), :])
        # 只预测原始张力和振动信号
        y.append(data[i + time_steps, :2])  
    return np.array(X), np.array(y)

print("创建监督学习数据集...")
X, y = create_dataset(scaled_data, TIME_STEPS)

# 详细分析时序数据集结构
def analyze_time_series_dataset(X, y, features, TIME_STEPS):
    """
    分析传入TCN的时序数据集结构
    """
    print("\n" + "="*80)
    print("传入TCN的时序数据集结构分析")
    print("="*80)
    
    print(f"输入数据 X 形状: {X.shape}")
    print(f"  - 样本数量: {X.shape[0]}")
    print(f"  - 时间步长: {X.shape[1]} (TIME_STEPS)")
    print(f"  - 特征维度: {X.shape[2]} (14个特征)")
    
    print(f"\n输出数据 y 形状: {y.shape}")
    print(f"  - 样本数量: {y.shape[0]}")
    print(f"  - 输出维度: {y.shape[1]} (张力+振动)")
    
    print(f"\n时序窗口说明:")
    print(f"- 每个样本包含连续{TIME_STEPS}个时间步的14维特征")
    print(f"- 预测目标是第{TIME_STEPS+1}个时间步的张力和振动值")
    
    # 展示第一个样本的结构
    print(f"\n第1个样本的时序数据结构:")
    print("-" * 100)
    print(f"{'时间步':<6} {'张力':<8} {'振动':<8} {'交互':<8} {'卡尔曼特征(6个)':<20} {'频域特征(5个)':<15}")
    print("-" * 100)
    
    sample_0 = X[0]  # 形状: (120, 14)
    for t in range(min(10, TIME_STEPS)):  # 只显示前10个时间步
        row = f"t-{TIME_STEPS-t-1:<4} "
        row += f"{sample_0[t, 0]:>7.3f} "  # tension
        row += f"{sample_0[t, 1]:>7.3f} "  # vibration
        row += f"{sample_0[t, 2]:>7.3f} "  # interaction
        
        # 卡尔曼特征 (索引3-8)
        kalman_vals = [f"{sample_0[t, i]:.2f}" for i in range(3, 9)]
        row += f"{', '.join(kalman_vals):<20} "
        
        # 频域特征 (索引9-13)
        freq_vals = [f"{sample_0[t, i]:.2f}" for i in range(9, 14)]
        row += f"{', '.join(freq_vals):<15}"
        
        print(row)
    
    if TIME_STEPS > 10:
        print(f"... (省略中间{TIME_STEPS-10}个时间步)")
    
    print(f"\n预测目标 (第{TIME_STEPS+1}个时间步):")
    print(f"- 张力: {y[0, 0]:.3f}")
    print(f"- 振动: {y[0, 1]:.3f}")
    
    # 数据统计信息
    print(f"\n数据统计信息:")
    print("-" * 60)
    print(f"输入数据 X:")
    print(f"  - 最小值: {np.min(X):.3f}")
    print(f"  - 最大值: {np.max(X):.3f}")
    print(f"  - 均值: {np.mean(X):.3f}")
    print(f"  - 标准差: {np.std(X):.3f}")
    
    print(f"\n输出数据 y:")
    print(f"  - 张力范围: [{np.min(y[:, 0]):.3f}, {np.max(y[:, 0]):.3f}]")
    print(f"  - 振动范围: [{np.min(y[:, 1]):.3f}, {np.max(y[:, 1]):.3f}]")
    
    print("="*80)

# 分析时序数据集
analyze_time_series_dataset(X, y, features, TIME_STEPS)

# 划分数据集
train_size = int(len(X) * (1 - TEST_SIZE - VAL_SIZE))
val_size = int(len(X) * VAL_SIZE)

X_train, y_train = X[:train_size], y[:train_size]
X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]

# 转换为PyTorch张量
X_train = torch.FloatTensor(X_train)
y_train = torch.FloatTensor(y_train)
X_val = torch.FloatTensor(X_val)
y_val = torch.FloatTensor(y_val)
X_test = torch.FloatTensor(X_test)
y_test = torch.FloatTensor(y_test)

# 创建数据加载器
train_dataset = TensorDataset(X_train, y_train)
val_dataset = TensorDataset(X_val, y_val)
test_dataset = TensorDataset(X_test, y_test)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

print(f"训练集形状: {X_train.shape}, 验证集形状: {X_val.shape}, 测试集形状: {X_test.shape}")
print(f"输入特征数: {X_train.shape[-1]}")

# 6. 模型初始化和加载 -----------------------------------------------------------
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 初始化融合模型
model = TCN_xLSTM_Fusion(
    input_size=X_train.shape[-1], 
    tcn_channels=TCN_CHANNELS,
    tcn_kernel_size=TCN_KERNEL_SIZE,
    tcn_blocks=TCN_BLOCKS,
    hidden_size=128, 
    num_layers=2, 
    output_size=2,  # 输出张力和振动
    dropout=0.2
).to(device)

# 计算模型参数数量
total_params = sum(p.numel() for p in model.parameters())
print(f"模型总参数数量: {total_params:,}")

# 检查是否存在已训练的模型
import os
model_path = 'tcn_xlstm_fusion_model.pth'

if os.path.exists(model_path):
    print(f"发现已训练模型 {model_path}，直接加载...")
    model.load_state_dict(torch.load(model_path, map_location=device))
    print("模型加载完成！")
else:
    print("未发现已训练模型，开始训练...")
    
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.1)

    # 训练循环
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0

    start_time = time.time()

    for epoch in range(EPOCHS):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 梯度裁剪
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        scheduler.step(val_loss)
        
        # 早停机制
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), model_path)
            print(f"保存最佳模型，验证损失: {best_val_loss:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= 10:
                print(f"Early stopping at epoch {epoch+1}")
                break
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{EPOCHS}], Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

    training_time = time.time() - start_time
    print(f"训练完成，耗时: {training_time:.2f}秒")

    # 绘制训练过程
    plt.figure(figsize=(12.8, 7.0))
    plt.plot(train_losses, label='训练损失')
    plt.plot(val_losses, label='验证损失')
    plt.title('TCN-xLSTM融合模型损失变化')
    plt.ylabel('MSE损失')
    plt.xlabel('Epoch')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    # plt.savefig('tcn_xlstm_training_loss.png', dpi=300)
    plt.show()

# 7. 改进的异常点检测功能 ----------------------------------------------------
def detect_enhanced_anomalies(model, dataloader, scalers, device, features, z_threshold=1.27):
    model.eval()
    predictions = []
    actuals = []
    
    with torch.no_grad():
        for batch_x, batch_y in dataloader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            outputs = model(batch_x)
            
            # 保存预测值和实际值
            predictions.append(outputs.cpu().numpy())
            actuals.append(batch_y.cpu().numpy())
    
    predictions = np.concatenate(predictions, axis=0)
    actuals = np.concatenate(actuals, axis=0)
    
    # 反归一化
    tension_pred_rescaled = scalers['tension'].inverse_transform(predictions[:, 0].reshape(-1, 1)).flatten()
    vibration_pred_rescaled = scalers['vibration'].inverse_transform(predictions[:, 1].reshape(-1, 1)).flatten()
    
    tension_actual_rescaled = scalers['tension'].inverse_transform(actuals[:, 0].reshape(-1, 1)).flatten()
    vibration_actual_rescaled = scalers['vibration'].inverse_transform(actuals[:, 1].reshape(-1, 1)).flatten()
    
    # 计算每个特征的误差
    tension_errors = np.abs(tension_pred_rescaled - tension_actual_rescaled)
    vibration_errors = np.abs(vibration_pred_rescaled - vibration_actual_rescaled)
    
    # 自适应权重调整
    tension_std = np.std(tension_errors)
    vibration_std = np.std(vibration_errors)
    
    # 根据误差波动性调整权重 - 波动小的信号权重更高
    tension_weight = vibration_std / (tension_std + vibration_std + 1e-8)
    vibration_weight = tension_std / (tension_std + vibration_std + 1e-8)
    
    # 归一化权重
    total_weight = tension_weight + vibration_weight
    tension_weight /= total_weight
    vibration_weight /= total_weight
    
    print(f"自适应权重 - 张力: {tension_weight:.4f}, 振动: {vibration_weight:.4f}")
    
    # 计算融合误差
    fused_errors = (tension_weight * tension_errors) + (vibration_weight * vibration_errors)
    
    # 计算融合误差的Z-score
    z_scores = np.abs(stats.zscore(fused_errors))
    anomaly_indices = np.where(z_scores > z_threshold)[0]
    
    # 计算动态阈值
    dynamic_threshold = np.median(z_scores) + 2.5 * stats.median_abs_deviation(z_scores)
    dynamic_anomalies = np.where(z_scores > dynamic_threshold)[0]
    
    return (tension_pred_rescaled, vibration_pred_rescaled, 
            tension_actual_rescaled, vibration_actual_rescaled, 
            fused_errors, anomaly_indices, z_scores, dynamic_threshold,
            tension_weight, vibration_weight)

# ====================== 知识推理规则引擎 ======================
class PhysicsEngine:
    """物理引擎：基于力学规则验证异常点"""
    def __init__(self):
        """
        简化的物理引擎，专注于力学规则
        """
        self.history = []

    def validate_anomaly(self, index, tension_value, vibration_value,
                         tension_change, vibration_change, freq_features):
        """
        基于力学规则验证异常点
        :return: bool, 是否为真实异常
        """
        freq_mean, freq_std, freq_max, freq_dominant, freq_bandwidth = freq_features

        # 力学规则1: 张力-振动耦合关系（胡克定律）
        # 根据胡克定律，张力变化会影响振动频率
        if abs(tension_change) > 1.0:  # 显著张力变化
            # 张力增加时，频率应该增加；张力减少时，频率应该减少
            if tension_change > 0 and freq_dominant < 10:  # 张力增加但频率很低
                self.history.append((index, "张力-振动耦合异常", f"张力增加{tension_change:.3f}但频率低{freq_dominant:.3f}"))
                return True
            elif tension_change < -1.0 and freq_dominant > 80:  # 张力大幅减少但频率很高
                self.history.append((index, "张力-振动耦合异常", f"张力减少{tension_change:.3f}但频率高{freq_dominant:.3f}"))
                return True

        # 力学规则2: 动态平衡规则
        # 系统在正常工作时应保持动态平衡
        if abs(tension_change) > 3.0 and abs(vibration_change) < 0.05:
            # 张力变化很大但振动几乎不变，违反动态平衡
            self.history.append((index, " ", f"张力变化{tension_change:.3f}振动变化{vibration_change:.3f}"))
            return True

        # 力学规则3: 能量守恒规则
        # 振动能量与张力变化应该有合理的关系
        vibration_energy = freq_max * freq_std  # 简化的振动能量
        if vibration_energy > 100:  # 振动能量过高
            if abs(tension_change) < 0.5:  # 但张力变化很小
                self.history.append((index, "能量守恒异常", f"高振动能量{vibration_energy:.3f}但张力变化小{tension_change:.3f}"))
                return True

        # 通过所有力学规则检查，确认为真实异常
        return True
        
    def save_history(self, filename="physics_engine_history.csv"):
        """保存物理引擎验证历史"""
        df = pd.DataFrame(self.history, columns=['Index', 'Reason', 'Value'])
        df.to_csv(filename, index=False)
        print(f"物理引擎验证历史已保存到 {filename}")

class CaseEngine:
    """案例引擎：基于上下文匹配验证异常点"""
    def __init__(self, similarity_threshold=0.6):
        """
        简化的案例引擎，专注于工况条件的相似性
        :param similarity_threshold: 相似度阈值，降低到0.6使其更容易匹配
        """
        self.similarity_threshold = similarity_threshold
        self.case_library = []  # 存储工况上下文

        # 预定义一些典型的异常工况模式
        self._init_typical_cases()

    def _init_typical_cases(self):
        """初始化典型异常工况案例"""
        # 典型异常模式1: 张力突降
        self.case_library.append({
            'pattern': 'tension_drop',
            'tension_range': (0, 50),
            'tension_change_range': (-10, -1),
            'vibration_range': (-5, 5),
            'freq_range': (0, 30),
            'description': '张力突然下降异常'
        })

        # 典型异常模式2: 高频振动
        self.case_library.append({
            'pattern': 'high_frequency',
            'tension_range': (40, 100),
            'tension_change_range': (-2, 2),
            'vibration_range': (-10, 10),
            'freq_range': (60, 200),
            'description': '高频振动异常'
        })

        # 典型异常模式3: 张力振动失衡
        self.case_library.append({
            'pattern': 'imbalance',
            'tension_range': (20, 80),
            'tension_change_range': (-5, 5),
            'vibration_range': (-8, 8),
            'freq_range': (10, 100),
            'description': '张力振动失衡异常'
        })

    def validate_anomaly(self, tension_value, vibration_value, tension_change, freq_dominant):
        """
        基于上下文匹配验证异常点
        :param tension_value: 张力值
        :param vibration_value: 振动值
        :param tension_change: 张力变化
        :param freq_dominant: 主要频率
        :return: bool, 是否匹配到相似工况
        """
        # 检查是否匹配任何已知的异常工况模式
        for case in self.case_library:
            # 检查张力范围
            if (case['tension_range'][0] <= tension_value <= case['tension_range'][1] and
                case['tension_change_range'][0] <= tension_change <= case['tension_change_range'][1] and
                case['vibration_range'][0] <= vibration_value <= case['vibration_range'][1] and
                case['freq_range'][0] <= freq_dominant <= case['freq_range'][1]):

                # print(f"匹配到工况模式: {case['description']}")
                return True

        # 如果没有匹配到任何模式，但参数在合理范围内，也认为是有效异常
        if (0 <= tension_value <= 100 and
            -10 <= vibration_value <= 10 and
            0 <= freq_dominant <= 200):
            return True

        return False

def apply_knowledge_rules(anomaly_indices, tension_values, vibration_values, freq_features,
                          physics_engine=None, case_engine=None):
    """
    简化的知识推理规则引擎 - 三引擎协同工作，处理连续异常点
    :param anomaly_indices: 初步检测到的异常点索引
    :param tension_values: 张力实际值数组
    :param vibration_values: 振动实际值数组
    :param freq_features: 频域特征数组 (N,5)
    :param physics_engine: 物理引擎实例
    :param case_engine: 案例引擎实例
    :return: (过滤后的异常点索引, 规则引擎排除的索引)
    """
    print(anomaly_indices)
    if len(anomaly_indices) == 0:
        return np.array([]), np.array([])

    # 首先分组连续异常点
    sorted_anomalies = np.sort(anomaly_indices)
    groups = []
    current_group = [sorted_anomalies[0]]

    for i in range(1, len(sorted_anomalies)):
        if sorted_anomalies[i] == sorted_anomalies[i-1] + 1:
            current_group.append(sorted_anomalies[i])
        else:
            groups.append(current_group)
            current_group = [sorted_anomalies[i]]
    groups.append(current_group)

    filtered_anomalies = []
    normal_indices = []

    print(f"开始处理 {len(anomaly_indices)} 个初步异常点，分为 {len(groups)} 组...")

    for group_idx, group in enumerate(groups):
        print(f"\n处理第 {group_idx+1} 组: {group} (共{len(group)}个点)")

        # 连续异常点处理策略 - 更严格的过滤
        if len(group) == 2:  # 2个连续点 - 全部忽略
            print(f"2个连续异常点({group})，全部忽略")
            normal_indices.extend(group)

        elif len(group) == 3:  # 3个连续点 - 只保留中间的1个
            key_point = group[1]  # 保留中间点
            non_key_points = [group[0], group[2]]  # 忽略首尾

            print(f"3个连续异常点({group})，只保留中间点: {key_point}")
            normal_indices.extend(non_key_points)

            # 对中间点进行验证
            if validate_single_point(key_point, tension_values, vibration_values, freq_features,
                                   physics_engine, case_engine):
                filtered_anomalies.append(key_point)
            else:
                normal_indices.append(key_point)

        elif len(group) == 4:  # 4个连续点 - 保留首尾2个
            key_points = [group[0], group[-1]]  # 起点和终点
            non_key_points = [group[1], group[2]]  # 忽略中间2个

            print(f"4个连续异常点({group})，保留首尾点: {key_points}")
            normal_indices.extend(non_key_points)

            # 对关键点进行验证
            for idx in key_points:
                if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                       physics_engine, case_engine):
                    filtered_anomalies.append(idx)
                else:
                    normal_indices.append(idx)

        elif len(group) >= 5:  # 5个及以上连续点 - 只保留关键点，最多4个
            # 只保留起点、终点和关键转折点，最多4个点
            key_points = select_key_points(group, tension_values, max_points=4)
            non_key_points = [idx for idx in group if idx not in key_points]

            print(f"连续异常点过多({len(group)}个)，只保留关键点: {key_points}")
            normal_indices.extend(non_key_points)

            # 对关键点进行验证
            for idx in key_points:
                if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                       physics_engine, case_engine):
                    filtered_anomalies.append(idx)
                else:
                    normal_indices.append(idx)

        else:  # 单个异常点
            idx = group[0]
            if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                   physics_engine, case_engine):
                filtered_anomalies.append(idx)
            else:
                normal_indices.append(idx)

    print(f"知识推理引擎总结: 确认异常点 {len(filtered_anomalies)} 个, 排除误报 {len(normal_indices)} 个")
    return np.array(filtered_anomalies), np.array(normal_indices)

def select_key_points(group, tension_values, max_points=4):
    """
    从连续异常点组中选择关键点（起点、终点、转折点）
    :param group: 连续异常点索引列表
    :param tension_values: 张力值数组
    :param max_points: 最大保留点数
    :return: 关键点索引列表
    """
    if len(group) <= max_points:
        return group

    key_points = []

    # 1. 始终保留起点和终点
    key_points.append(group[0])   # 起点
    key_points.append(group[-1])  # 终点

    # 2. 如果还能保留更多点，找转折点
    if max_points > 2:
        remaining_slots = max_points - 2

        # 找到最大值和最小值点
        values = [tension_values[idx] for idx in group]
        max_idx = group[np.argmax(values)]
        min_idx = group[np.argmin(values)]

        # 添加极值点（如果不是起点或终点）
        if max_idx not in key_points and remaining_slots > 0:
            key_points.append(max_idx)
            remaining_slots -= 1

        if min_idx not in key_points and remaining_slots > 0:
            key_points.append(min_idx)
            remaining_slots -= 1

        # 如果还有空位，添加中间的关键转折点
        if remaining_slots > 0:
            # 找变化最大的点
            max_change_idx = None
            max_change = 0

            for i in range(1, len(group) - 1):
                idx = group[i]
                if idx not in key_points:
                    # 计算该点的变化幅度
                    prev_val = tension_values[group[i-1]]
                    curr_val = tension_values[idx]
                    next_val = tension_values[group[i+1]]

                    change = abs(curr_val - prev_val) + abs(next_val - curr_val)
                    if change > max_change:
                        max_change = change
                        max_change_idx = idx

            if max_change_idx is not None:
                key_points.append(max_change_idx)

    return sorted(key_points)


def validate_single_point(idx, tension_values, vibration_values, freq_features,
                         physics_engine, case_engine):
    """
    验证单个异常点是否为真实异常
    """
    # 工况转换点排除规则：排除上提结束下放开始的正常转换点
    if idx in [78,427, 809]:
        print(f"异常点 {idx} 排除: 上提结束下放开始的正常工况转换点")
        return False

    current_value = tension_values[idx]

    # 计算变化量
    if idx > 0:
        tension_change = tension_values[idx] - tension_values[idx-1]
        vibration_change = vibration_values[idx] - vibration_values[idx-1]
    else:
        tension_change = 0
        vibration_change = 0

    freq_dominant = freq_features[idx][3]  # 主要频率

    # 详细的上升规则引擎：基于知识推理.py的规则
    rule_passed = True

    # 上升规则1: 明显上升趋势检查
    if idx > 0 and idx < len(tension_values) - 1:
        prev_value = tension_values[idx-1]
        next_value = tension_values[idx+1]

        prev_diff = current_value - prev_value
        next_diff = next_value - current_value

        # 条件1: 明显上升趋势
        if prev_diff > 0.05 and next_diff > -0.3:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 明显上升趋势 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

        # 条件2: 微小上升或平稳
        elif prev_diff > -0.1 and next_diff > -0.1 and abs(prev_diff) < 1.0 and abs(next_diff) < 1.0:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 微小变化/平稳 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

        # 条件3: 从低值恢复的上升
        elif prev_value < current_value and current_value > 40:  # 从低值恢复
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 从低值恢复 (从{prev_value:.3f}升至{current_value:.3f})")

        # 条件4: 上升趋势中的点（放宽回落容忍度）
        elif prev_diff > 0.05 and next_diff > -0.2:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 处于上升趋势 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

    # 上升规则2: 微小波动点
    if abs(tension_change) < 0.5 and abs(vibration_change) < 0.5:
        rule_passed = False
        print(f"规则引擎排除点 {idx}: 微小波动点 (张力变化{tension_change:+.3f}, 振动变化{vibration_change:+.3f})")

    # 上升规则3: 整体递增趋势检查（针对连续点）
    if idx > 2 and idx < len(tension_values) - 2:
        # 检查前后5个点的整体趋势
        window_start = max(0, idx - 2)
        window_end = min(len(tension_values), idx + 3)
        window_values = tension_values[window_start:window_end]

        if len(window_values) >= 3:
            overall_increase = window_values[-1] - window_values[0]
            avg_increase = overall_increase / (len(window_values) - 1)

            if avg_increase > 0.1:  # 平均增幅阈值
                rule_passed = False
                print(f"规则引擎排除点 {idx}: 局部递增趋势 (平均增幅{avg_increase:.3f})")

    # 如果通过所有上升规则检查，进入三引擎验证
    if rule_passed:
        # 物理引擎验证
        physics_valid = True
        if physics_engine:
            physics_valid = physics_engine.validate_anomaly(
                idx, current_value, vibration_values[idx],
                tension_change, vibration_change, freq_features[idx]
            )

        # 案例引擎验证
        case_valid = True
        if case_engine:
            case_valid = case_engine.validate_anomaly(
                current_value, vibration_values[idx], tension_change, freq_dominant
            )

        # 三引擎协同决策：只要有一个引擎确认就保留
        if physics_valid or case_valid:
            print(f"异常点 {idx} 确认: 物理引擎={physics_valid}, 案例引擎={case_valid}")
            return True
        else:
            print(f"异常点 {idx} 排除: 未通过引擎验证")
            return False
    else:
        print(f"异常点 {idx} 排除: 未通过上升规则")
        return False



# 确保模型处于评估模式
model.eval()

# xLSTM输出完整曲线绘制 -----------------------------------------------------------
def plot_xlstm_complete_outputs(model, train_loader, val_loader, test_loader, scalers, device, features, plot_dirs):
    """
    绘制xLSTM模型在训练集、验证集、测试集上的完整输出曲线
    """
    print("开始绘制xLSTM模型完整输出曲线...")
    
    model.eval()
    
    # 收集所有数据集的预测结果
    datasets = {
        'train': train_loader,
        'val': val_loader, 
        'test': test_loader
    }
    
    all_predictions = {}
    all_actuals = {}
    
    for dataset_name, dataloader in datasets.items():
        predictions = []
        actuals = []
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                outputs = model(batch_x)
                
                predictions.append(outputs.cpu().numpy())
                actuals.append(batch_y.cpu().numpy())
        
        all_predictions[dataset_name] = np.concatenate(predictions, axis=0)
        all_actuals[dataset_name] = np.concatenate(actuals, axis=0)
    
    # 反归一化预测结果
    datasets_rescaled = {}
    for dataset_name in datasets.keys():
        tension_pred_rescaled = scalers['tension'].inverse_transform(
            all_predictions[dataset_name][:, 0].reshape(-1, 1)).flatten()
        vibration_pred_rescaled = scalers['vibration'].inverse_transform(
            all_predictions[dataset_name][:, 1].reshape(-1, 1)).flatten()
        
        tension_actual_rescaled = scalers['tension'].inverse_transform(
            all_actuals[dataset_name][:, 0].reshape(-1, 1)).flatten()
        vibration_actual_rescaled = scalers['vibration'].inverse_transform(
            all_actuals[dataset_name][:, 1].reshape(-1, 1)).flatten()
        
        datasets_rescaled[dataset_name] = {
            'tension_pred': tension_pred_rescaled,
            'vibration_pred': vibration_pred_rescaled,
            'tension_actual': tension_actual_rescaled,
            'vibration_actual': vibration_actual_rescaled
        }
    
    # 拼接所有数据集的结果
    full_tension_pred = np.concatenate([
        datasets_rescaled['train']['tension_pred'],
        datasets_rescaled['val']['tension_pred'],
        datasets_rescaled['test']['tension_pred']
    ])
    
    full_tension_actual = np.concatenate([
        datasets_rescaled['train']['tension_actual'],
        datasets_rescaled['val']['tension_actual'],
        datasets_rescaled['test']['tension_actual']
    ])
    
    full_vibration_pred = np.concatenate([
        datasets_rescaled['train']['vibration_pred'],
        datasets_rescaled['val']['vibration_pred'],
        datasets_rescaled['test']['vibration_pred']
    ])
    
    full_vibration_actual = np.concatenate([
        datasets_rescaled['train']['vibration_actual'],
        datasets_rescaled['val']['vibration_actual'],
        datasets_rescaled['test']['vibration_actual']
    ])
    
    # 计算数据集分界点
    train_end = len(datasets_rescaled['train']['tension_pred'])
    val_end = train_end + len(datasets_rescaled['val']['tension_pred'])
    test_end = val_end + len(datasets_rescaled['test']['tension_pred'])
    
    # 1. 绘制完整张力输出曲线
    plt.figure(figsize=(12.8, 7.0))
    plt.plot(full_tension_actual, label='张力真实值', color='blue', linewidth=2, alpha=0.8)
    plt.plot(full_tension_pred, label='xLSTM张力预测值', color='red', linewidth=2, alpha=0.8, linestyle='--')
    
    # 添加数据集分界线
    plt.axvline(x=train_end, color='green', linestyle=':', linewidth=2, alpha=0.7, label='训练集|验证集')
    plt.axvline(x=val_end, color='orange', linestyle=':', linewidth=2, alpha=0.7, label='验证集|测试集')
    
    # 添加数据集标注
    plt.text(train_end//2, plt.ylim()[1]*0.9, '训练集', fontsize=12, ha='center', 
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    plt.text((train_end + val_end)//2, plt.ylim()[1]*0.9, '验证集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
    plt.text((val_end + test_end)//2, plt.ylim()[1]*0.9, '测试集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
    
    plt.title('xLSTM模型张力输出完整曲线', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('张力值 (KN)', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    save_path = os.path.join(plot_dirs['xlstm'], 'xlstm_complete_tension_output.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()  # 实时预览
    plt.close()
    
    # 2. 绘制完整振动输出曲线
    plt.figure(figsize=(12.8, 7.0))
    plt.plot(full_vibration_actual, label='振动真实值', color='green', linewidth=2, alpha=0.8)
    plt.plot(full_vibration_pred, label='xLSTM振动预测值', color='purple', linewidth=2, alpha=0.8, linestyle='--')
    
    # 添加数据集分界线
    plt.axvline(x=train_end, color='green', linestyle=':', linewidth=2, alpha=0.7, label='训练集|验证集')
    plt.axvline(x=val_end, color='orange', linestyle=':', linewidth=2, alpha=0.7, label='验证集|测试集')
    
    # 添加数据集标注
    plt.text(train_end//2, plt.ylim()[1]*0.9, '训练集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    plt.text((train_end + val_end)//2, plt.ylim()[1]*0.9, '验证集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
    plt.text((val_end + test_end)//2, plt.ylim()[1]*0.9, '测试集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
    
    plt.title('xLSTM模型振动输出完整曲线', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('振动值 (G)', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    save_path = os.path.join(plot_dirs['xlstm'], 'xlstm_complete_vibration_output.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 3. 绘制双输出对比图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12.8, 7.0))
    
    # 张力子图
    ax1.plot(full_tension_actual, label='张力真实值', color='blue', linewidth=1.5, alpha=0.8)
    ax1.plot(full_tension_pred, label='xLSTM张力预测值', color='red', linewidth=1.5, alpha=0.8, linestyle='--')
    ax1.axvline(x=train_end, color='green', linestyle=':', linewidth=1, alpha=0.7)
    ax1.axvline(x=val_end, color='orange', linestyle=':', linewidth=1, alpha=0.7)
    ax1.set_title('张力输出对比', fontsize=12, fontweight='bold')
    ax1.set_ylabel('张力值 (KN)', fontsize=10)
    ax1.legend(fontsize=9)
    ax1.grid(True, linestyle='--', alpha=0.5)
    
    # 振动子图
    ax2.plot(full_vibration_actual, label='振动真实值', color='green', linewidth=1.5, alpha=0.8)
    ax2.plot(full_vibration_pred, label='xLSTM振动预测值', color='purple', linewidth=1.5, alpha=0.8, linestyle='--')
    ax2.axvline(x=train_end, color='green', linestyle=':', linewidth=1, alpha=0.7)
    ax2.axvline(x=val_end, color='orange', linestyle=':', linewidth=1, alpha=0.7)
    ax2.set_title('振动输出对比', fontsize=12, fontweight='bold')
    ax2.set_xlabel('时间序列', fontsize=10)
    ax2.set_ylabel('振动值 (G)', fontsize=10)
    ax2.legend(fontsize=9)
    ax2.grid(True, linestyle='--', alpha=0.5)
    
    plt.suptitle('xLSTM模型双输出完整曲线对比', fontsize=14, fontweight='bold')
    plt.tight_layout()
    save_path = os.path.join(plot_dirs['xlstm'], 'xlstm_complete_dual_output.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    plt.close()
    
    # 4. 计算并显示各数据集的性能指标
    print("\n=== xLSTM模型各数据集性能指标 ===")
    for dataset_name in ['train', 'val', 'test']:
        data = datasets_rescaled[dataset_name]
        
        tension_rmse = np.sqrt(mean_squared_error(data['tension_actual'], data['tension_pred']))
        tension_mae = mean_absolute_error(data['tension_actual'], data['tension_pred'])
        
        vibration_rmse = np.sqrt(mean_squared_error(data['vibration_actual'], data['vibration_pred']))
        vibration_mae = mean_absolute_error(data['vibration_actual'], data['vibration_pred'])
        
        print(f"{dataset_name.upper()}集:")
        print(f"  张力 - RMSE: {tension_rmse:.4f}, MAE: {tension_mae:.4f}")
        print(f"  振动 - RMSE: {vibration_rmse:.4f}, MAE: {vibration_mae:.4f}")
    
    # 5. 绘制误差分布图
    plt.figure(figsize=(12.8, 7.0))
    
    # 计算误差
    tension_errors = full_tension_pred - full_tension_actual
    vibration_errors = full_vibration_pred - full_vibration_actual
    
    plt.subplot(2, 1, 1)
    plt.plot(tension_errors, color='red', linewidth=1.5, alpha=0.8, label='张力预测误差')
    plt.axvline(x=train_end, color='green', linestyle=':', linewidth=1, alpha=0.7)
    plt.axvline(x=val_end, color='orange', linestyle=':', linewidth=1, alpha=0.7)
    plt.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.5)
    plt.title('张力预测误差', fontsize=12, fontweight='bold')
    plt.ylabel('误差值 (KN)', fontsize=10)
    plt.legend(fontsize=9)
    plt.grid(True, linestyle='--', alpha=0.5)
    
    plt.subplot(2, 1, 2)
    plt.plot(vibration_errors, color='purple', linewidth=1.5, alpha=0.8, label='振动预测误差')
    plt.axvline(x=train_end, color='green', linestyle=':', linewidth=1, alpha=0.7)
    plt.axvline(x=val_end, color='orange', linestyle=':', linewidth=1, alpha=0.7)
    plt.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.5)
    plt.title('振动预测误差', fontsize=12, fontweight='bold')
    plt.xlabel('时间序列', fontsize=10)
    plt.ylabel('误差值 (G)', fontsize=10)
    plt.legend(fontsize=9)
    plt.grid(True, linestyle='--', alpha=0.5)
    
    plt.suptitle('xLSTM模型预测误差分析', fontsize=14, fontweight='bold')
    plt.tight_layout()
    save_path = os.path.join(plot_dirs['xlstm'], 'xlstm_prediction_errors.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()
    plt.close()
    
    return {
        'full_results': {
            'tension_pred': full_tension_pred,
            'tension_actual': full_tension_actual,
            'vibration_pred': full_vibration_pred,
            'vibration_actual': full_vibration_actual
        },
        'dataset_results': datasets_rescaled,
        'boundaries': {'train_end': train_end, 'val_end': val_end, 'test_end': test_end}
    }

# 创建图表存储目录
plot_dirs = create_plot_directories()

# 绘制xLSTM完整输出曲线
print("绘制xLSTM模型完整输出曲线...")
xlstm_output_results = plot_xlstm_complete_outputs(model, train_loader, val_loader, test_loader, scalers, device, features, plot_dirs)

# TCN输出完整曲线绘制 -----------------------------------------------------------
def plot_tcn_complete_outputs(model, train_loader, val_loader, test_loader, device, features, plot_dirs):
    """
    绘制TCN模块的完整输出曲线
    """
    print("开始绘制TCN模块完整输出曲线...")
    
    model.eval()
    
    # 收集所有数据集的TCN输出
    datasets = {
        'train': train_loader,
        'val': val_loader, 
        'test': test_loader
    }
    
    all_tcn_outputs = {}
    all_inputs = {}
    
    for dataset_name, dataloader in datasets.items():
        tcn_outputs = []
        inputs = []
        
        with torch.no_grad():
            for batch_x, batch_y in dataloader:
                batch_x = batch_x.to(device)
                
                # 只通过TCN模块获取输出
                tcn_out = model.tcn(batch_x)  # shape: (batch, seq_len, tcn_channels)
                
                tcn_outputs.append(tcn_out.cpu().numpy())
                inputs.append(batch_x.cpu().numpy())
        
        all_tcn_outputs[dataset_name] = np.concatenate(tcn_outputs, axis=0)
        all_inputs[dataset_name] = np.concatenate(inputs, axis=0)
    
    # 拼接所有数据集的结果
    full_tcn_output = np.concatenate([
        all_tcn_outputs['train'],
        all_tcn_outputs['val'],
        all_tcn_outputs['test']
    ], axis=0)  # shape: (total_samples, seq_len, tcn_channels)
    
    full_input = np.concatenate([
        all_inputs['train'],
        all_inputs['val'],
        all_inputs['test']
    ], axis=0)  # shape: (total_samples, seq_len, input_features)
    
    # 计算数据集分界点
    train_end = len(all_tcn_outputs['train'])
    val_end = train_end + len(all_tcn_outputs['val'])
    test_end = val_end + len(all_tcn_outputs['test'])
    
    # 1. 绘制TCN输出通道的平均激活值
    plt.figure(figsize=(12.8, 7.0))
    
    # 计算每个样本在所有时间步和通道上的平均激活值
    tcn_mean_activation = np.mean(full_tcn_output, axis=(1, 2))  # shape: (total_samples,)
    
    plt.plot(tcn_mean_activation, color='blue', linewidth=2, alpha=0.8, label='TCN平均激活值')
    
    # 添加数据集分界线
    plt.axvline(x=train_end, color='green', linestyle=':', linewidth=2, alpha=0.7, label='训练集|验证集')
    plt.axvline(x=val_end, color='orange', linestyle=':', linewidth=2, alpha=0.7, label='验证集|测试集')
    
    # 添加数据集标注
    plt.text(train_end//2, plt.ylim()[1]*0.9, '训练集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    plt.text((train_end + val_end)//2, plt.ylim()[1]*0.9, '验证集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
    plt.text((val_end + test_end)//2, plt.ylim()[1]*0.9, '测试集', fontsize=12, ha='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.7))
    
    plt.title('TCN模块平均激活值完整曲线', fontsize=14, fontweight='bold')
    plt.xlabel('样本索引', fontsize=12)
    plt.ylabel('平均激活值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    save_path = os.path.join(plot_dirs['tcn'], 'tcn_complete_mean_activation.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()  # 实时预览
    plt.close()
    
    # 2. 绘制TCN各通道的激活强度（百分比显示）
    plt.figure(figsize=(12.8, 7.0))
    
    # 计算每个通道在所有样本和时间步上的平均激活值
    tcn_channel_means = np.mean(full_tcn_output, axis=(0, 1))  # shape: (tcn_channels,)
    
    # 转换为百分比形式
    total_activation = np.sum(tcn_channel_means)
    tcn_channel_percentages = (tcn_channel_means / total_activation) * 100
    
    channels = range(len(tcn_channel_percentages))
    plt.bar(channels, tcn_channel_percentages, color='skyblue', alpha=0.8, edgecolor='navy')
    plt.title('TCN各通道激活强度占比（百分比）', fontsize=14, fontweight='bold')
    plt.xlabel('TCN通道索引', fontsize=12)
    plt.ylabel('激活强度占比 (%)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7, axis='y')
    
    # 标注最活跃的几个通道
    top_channels = np.argsort(tcn_channel_percentages)[-5:]  # 最活跃的5个通道
    for i, ch in enumerate(top_channels):
        plt.annotate(f'Ch{ch}\n{tcn_channel_percentages[ch]:.2f}%', 
                    xy=(ch, tcn_channel_percentages[ch]), 
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, ha='left',
                    bbox=dict(boxstyle="round,pad=0.2", facecolor="yellow", alpha=0.7))
    
    plt.tight_layout()
    # plt.savefig('tcn_channel_activation_strength.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 3. 绘制TCN时序特征演化（包含百分比显示）
    plt.figure(figsize=(12.8, 7.0))
    
    # 选择几个代表性样本展示TCN输出的时序演化
    sample_indices = [len(full_tcn_output)//4, len(full_tcn_output)//2, 3*len(full_tcn_output)//4]
    colors = ['blue', 'red', 'green']
    
    for i, sample_idx in enumerate(sample_indices):
        # 计算该样本在所有通道上的平均激活值随时间的变化
        sample_tcn_output = full_tcn_output[sample_idx]  # shape: (seq_len, tcn_channels)
        temporal_mean = np.mean(sample_tcn_output, axis=1)  # shape: (seq_len,)
        
        # 转换为相对百分比（相对于该样本的最大值）
        max_activation = np.max(temporal_mean)
        if max_activation > 0:
            temporal_percentage = (temporal_mean / max_activation) * 100
        else:
            temporal_percentage = temporal_mean
        
        plt.plot(temporal_percentage, color=colors[i], linewidth=2, alpha=0.8, 
                label=f'样本{sample_idx} (位置{sample_idx/len(full_tcn_output)*100:.1f}%)')
    
    plt.title('TCN时序特征演化示例（相对百分比）', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('相对激活强度 (%)', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    # plt.savefig('tcn_temporal_evolution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 4. 绘制TCN输出与输入特征的关系
    fig, axes = plt.subplots(2, 2, figsize=(12.8, 7.0))
    axes = axes.flatten()
    
    # 选择几个重要的输入特征进行分析
    important_features = ['tension', 'vibration', 'interaction', 'freq_dominant']
    feature_indices = [features.index(feat) for feat in important_features if feat in features]
    
    for i, feat_idx in enumerate(feature_indices[:4]):
        if i < len(axes):
            ax = axes[i]
            
            # 计算输入特征的平均值（跨时间步）
            input_feature_mean = np.mean(full_input[:, :, feat_idx], axis=1)  # shape: (total_samples,)
            
            # 计算TCN输出的平均值（跨时间步和通道）
            tcn_output_mean = np.mean(full_tcn_output, axis=(1, 2))  # shape: (total_samples,)
            
            # 绘制散点图
            ax.scatter(input_feature_mean, tcn_output_mean, alpha=0.6, s=20, color='blue')
            
            # 计算相关系数
            correlation = np.corrcoef(input_feature_mean, tcn_output_mean)[0, 1]
            
            ax.set_title(f'{features[feat_idx]} vs TCN输出\n相关系数: {correlation:.3f}', 
                        fontsize=10, fontweight='bold')
            ax.set_xlabel(f'{features[feat_idx]}平均值', fontsize=9)
            ax.set_ylabel('TCN平均输出', fontsize=9)
            ax.grid(True, linestyle='--', alpha=0.3)
    
    # 隐藏多余的子图
    for i in range(len(feature_indices), len(axes)):
        axes[i].set_visible(False)
    
    plt.suptitle('TCN输出与输入特征关系分析', fontsize=14, fontweight='bold')
    plt.tight_layout()
    # plt.savefig('tcn_input_output_correlation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 5. 绘制TCN输出的统计分布
    plt.figure(figsize=(12.8, 7.0))
    
    # 分别计算各数据集的TCN输出分布
    dataset_colors = {'train': 'blue', 'val': 'green', 'test': 'red'}
    
    for dataset_name, tcn_output in all_tcn_outputs.items():
        # 展平所有维度
        flat_output = tcn_output.flatten()
        
        plt.hist(flat_output, bins=50, alpha=0.6, density=True, 
                label=f'{dataset_name.upper()}集', color=dataset_colors[dataset_name])
    
    plt.title('TCN输出值分布对比', fontsize=14, fontweight='bold')
    plt.xlabel('TCN输出值', fontsize=12)
    plt.ylabel('密度', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    # plt.savefig('tcn_output_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 6. 输出统计信息（包含百分比显示）
    print("\n=== TCN模块输出统计信息 ===")
    print(f"TCN输出形状: {full_tcn_output.shape}")
    print(f"TCN通道数: {full_tcn_output.shape[2]}")
    print(f"时间步长: {full_tcn_output.shape[1]}")
    
    # 计算各通道激活强度百分比
    tcn_channel_means = np.mean(full_tcn_output, axis=(0, 1))
    total_activation = np.sum(tcn_channel_means)
    tcn_channel_percentages = (tcn_channel_means / total_activation) * 100
    
    print(f"\nTCN各通道激活强度占比:")
    print(f"{'通道':<6} {'激活值':<12} {'占比(%)':<10}")
    print("-" * 30)
    for ch in range(len(tcn_channel_percentages)):
        print(f"Ch{ch:<4} {tcn_channel_means[ch]:<12.6f} {tcn_channel_percentages[ch]:<10.2f}")
    
    # 显示最活跃的前5个通道
    top_channels = np.argsort(tcn_channel_percentages)[-5:][::-1]  # 降序排列
    print(f"\n最活跃的前5个通道:")
    for i, ch in enumerate(top_channels):
        print(f"第{i+1}名: Ch{ch} - {tcn_channel_percentages[ch]:.2f}%")
    
    for dataset_name, tcn_output in all_tcn_outputs.items():
        flat_output = tcn_output.flatten()
        print(f"\n{dataset_name.upper()}集TCN输出统计:")
        print(f"  均值: {np.mean(flat_output):.6f}")
        print(f"  标准差: {np.std(flat_output):.6f}")
        print(f"  最小值: {np.min(flat_output):.6f}")
        print(f"  最大值: {np.max(flat_output):.6f}")
    
    return {
        'tcn_outputs': all_tcn_outputs,
        'full_tcn_output': full_tcn_output,
        'channel_percentages': tcn_channel_percentages,
        'channel_means': tcn_channel_means,
        'boundaries': {'train_end': train_end, 'val_end': val_end, 'test_end': test_end}
    }

# 绘制TCN完整输出曲线
print("绘制TCN模块完整输出曲线...")
tcn_output_results = plot_tcn_complete_outputs(model, train_loader, val_loader, test_loader, device, features, plot_dirs)

# TCN通道百分比贡献分析 -----------------------------------------------------------
def analyze_tcn_channel_contributions(tcn_output_results, top_n=10):
    """
    详细分析TCN各通道的百分比贡献
    """
    print("\n" + "="*80)
    print("TCN通道百分比贡献详细分析")
    print("="*80)
    
    channel_percentages = tcn_output_results['channel_percentages']
    channel_means = tcn_output_results['channel_means']
    
    # 按贡献度排序
    sorted_indices = np.argsort(channel_percentages)[::-1]  # 降序
    
    print(f"TCN总通道数: {len(channel_percentages)}")
    print(f"总激活值: {np.sum(channel_means):.6f}")
    print(f"\n前{top_n}个最重要通道的百分比贡献:")
    print("-" * 60)
    print(f"{'排名':<4} {'通道':<8} {'激活值':<12} {'百分比':<10} {'累积百分比':<12}")
    print("-" * 60)
    
    cumulative_percentage = 0
    for i, ch_idx in enumerate(sorted_indices[:top_n]):
        cumulative_percentage += channel_percentages[ch_idx]
        print(f"{i+1:<4} Ch{ch_idx:<6} {channel_means[ch_idx]:<12.6f} "
              f"{channel_percentages[ch_idx]:<10.2f}% {cumulative_percentage:<12.2f}%")
    
    # 分析通道贡献的分布特征
    print(f"\n通道贡献分布特征:")
    print("-" * 40)
    print(f"平均贡献度: {np.mean(channel_percentages):.2f}%")
    print(f"标准差: {np.std(channel_percentages):.2f}%")
    print(f"最大贡献度: {np.max(channel_percentages):.2f}%")
    print(f"最小贡献度: {np.min(channel_percentages):.2f}%")
    print(f"前10%通道贡献总和: {np.sum(sorted(channel_percentages, reverse=True)[:len(channel_percentages)//10]):.2f}%")
    print(f"前50%通道贡献总和: {np.sum(sorted(channel_percentages, reverse=True)[:len(channel_percentages)//2]):.2f}%")
    
    # 绘制通道贡献度分布饼图（前10个通道）
    plt.figure(figsize=(10, 8))
    
    top_10_indices = sorted_indices[:10]
    top_10_percentages = channel_percentages[top_10_indices]
    other_percentage = 100 - np.sum(top_10_percentages)
    
    # 准备饼图数据
    pie_labels = [f'Ch{i}' for i in top_10_indices] + ['其他通道']
    pie_values = list(top_10_percentages) + [other_percentage]
    pie_colors = plt.cm.Set3(np.linspace(0, 1, len(pie_values)))
    
    # 绘制饼图
    wedges, texts, autotexts = plt.pie(pie_values, labels=pie_labels, autopct='%1.1f%%',
                                      startangle=90, colors=pie_colors)
    
    # 美化文本
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
        autotext.set_fontsize(9)
    
    plt.title('TCN前10个通道激活强度占比分布', fontsize=14, fontweight='bold')
    plt.axis('equal')
    plt.tight_layout()
    # plt.savefig('tcn_top10_channels_pie.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("="*80)
    
    return {
        'sorted_indices': sorted_indices,
        'top_channels': sorted_indices[:top_n],
        'distribution_stats': {
            'mean': np.mean(channel_percentages),
            'std': np.std(channel_percentages),
            'max': np.max(channel_percentages),
            'min': np.min(channel_percentages)
        }
    }

# 执行TCN通道百分比贡献分析
tcn_contribution_analysis = analyze_tcn_channel_contributions(tcn_output_results, top_n=10)

# 在测试集上检测异常点
print("在测试集上检测异常点...")
(tension_pred, vibration_pred, 
 tension_actual, vibration_actual, 
 fused_errors, test_anomalies, 
 test_z_scores, dynamic_threshold,
 tension_weight, vibration_weight) = detect_enhanced_anomalies(
    model, test_loader, scalers, device, features, z_threshold=1.27
)

# 准备频域特征
freq_features_test = np.column_stack((
    fused_df['freq_mean'].values,
    fused_df['freq_std'].values,
    fused_df['freq_max'].values,
    fused_df['freq_dominant'].values,
    fused_df['freq_bandwidth'].values
))

# 初始化简化的物理引擎和案例引擎
physics_engine = PhysicsEngine()

case_engine = CaseEngine(similarity_threshold=0.6)

# 应用知识推理规则引擎 - 使用物理引擎和案例引擎
filtered_anomalies, normal_indices = apply_knowledge_rules(
    test_anomalies, 
    tension_actual,
    vibration_actual,
    freq_features_test,
    physics_engine=physics_engine,
    case_engine=case_engine
)

# 知识推理引擎输出完整曲线绘制 -----------------------------------------------------------
def plot_engine_results(physics_engine, case_engine, rule_results, tension_actual, vibration_actual, freq_features_test, plot_dirs):
    # 使用知识推理图表文件夹
    engine_output_dir = plot_dirs['knowledge']
    
    # 确保输出目录存在
    if not os.path.exists(engine_output_dir):
        os.makedirs(engine_output_dir)
    
    # 1. 物理引擎结果可视化
    plt.figure(figsize=(12.8, 7.0))
    # 将物理引擎历史记录转换为DataFrame
    physics_history = pd.DataFrame(physics_engine.history, columns=['Index', 'Reason', 'Value'])
    
    # 绘制张力和振动曲线
    plt.plot(tension_actual, label='张力值', color='blue', alpha=0.6)
    plt.plot(vibration_actual, label='振动值', color='red', alpha=0.6)
    
    # 标记异常点
    if len(physics_history) > 0:
        anomaly_indices = physics_history['Index'].values
        plt.scatter(anomaly_indices, tension_actual[anomaly_indices], 
                   color='red', marker='x', s=100, label='物理引擎异常点')
        
        # 添加异常原因标注
        for idx, reason in zip(anomaly_indices, physics_history['Reason']):
            plt.annotate(reason, (idx, tension_actual[idx]), 
                        xytext=(10, 10), textcoords='offset points',
                        fontsize=8, alpha=0.7)
    
    plt.title('物理引擎分析结果', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('数值', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(engine_output_dir, 'physics_engine_results.png'), dpi=100, bbox_inches='tight')
    plt.show()
    plt.close()

    # 2. 案例引擎结果可视化
    plt.figure(figsize=(12.8, 7.0))
    
    # 绘制张力曲线作为背景
    plt.plot(tension_actual, label='张力值', color='blue', alpha=0.3)
    
    # 绘制相似度阈值线
    plt.axhline(y=case_engine.similarity_threshold, color='r', linestyle='--', label='相似度阈值')
    
    # 标记匹配到的异常案例
    for case in case_engine.case_library:
        pattern_name = case['description']
        # 在张力范围内的点可能匹配此模式
        mask = (tension_actual >= case['tension_range'][0]) & (tension_actual <= case['tension_range'][1])
        if np.any(mask):
            plt.scatter(np.where(mask)[0], tension_actual[mask], 
                       alpha=0.5, label=pattern_name)
    
    plt.title('案例引擎分析结果', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('数值', fontsize=12)
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, linestyle='--', alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(engine_output_dir, 'case_engine_results.png'), dpi=100, bbox_inches='tight')
    plt.show()
    plt.close()

    # 3. 规则引擎结果可视化
    plt.figure(figsize=(12.8, 7.0))
    
    # 创建双y轴
    ax1 = plt.gca()
    ax2 = ax1.twinx()
    
    # 绘制张力曲线在左轴
    line1 = ax1.plot(tension_actual, label='张力值', color='blue', alpha=0.3)
    ax1.set_ylabel('张力值', color='blue', fontsize=12)
    
    # 绘制规则置信度在右轴
    line2 = ax2.plot(rule_results['confidence_score'], label='规则置信度', color='green', linewidth=2)
    ax2.set_ylabel('规则置信度', color='green', fontsize=12)
    
    # 标记异常点
    scatter = ax2.scatter(rule_results.index[rule_results['is_anomaly']], 
                         rule_results['confidence_score'][rule_results['is_anomaly']], 
                         color='red', marker='x', s=100, label='规则引擎异常点')
    
    # 合并图例
    lines = line1 + line2 + [scatter]
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, fontsize=10, loc='upper right')
    
    plt.title('规则引擎分析结果', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    
    # 设置网格
    ax1.grid(True, linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(engine_output_dir, 'rule_engine_results.png'), dpi=100, bbox_inches='tight')
    plt.show()
    plt.close()

# 保存物理引擎验证历史
physics_engine.save_history("physics_validation_history.csv")

# 获取规则引擎结果
rule_results = pd.DataFrame({
    'confidence_score': test_z_scores,
    'is_anomaly': np.isin(range(len(test_z_scores)), filtered_anomalies)
})

# 绘制三个引擎的单独结果
plot_engine_results(physics_engine, case_engine, rule_results, 
                   tension_actual, vibration_actual, freq_features_test, plot_dirs)

def plot_knowledge_reasoning_outputs(test_anomalies, filtered_anomalies, normal_indices, 
        tension_actual, vibration_actual, freq_features_test,
        physics_engine, case_engine, test_z_scores, plot_dirs):
    """
    绘制知识推理引擎（规则引擎、案例引擎、物理引擎）的完整输出曲线
    """
    print("开始绘制知识推理引擎完整输出曲线...")
    
    # 创建知识推理决策数组
    total_length = len(tension_actual)
    
    # 初始化决策数组：0=正常点，1=初步异常点，2=最终异常点，3=规则排除点
    reasoning_decisions = np.zeros(total_length)
    reasoning_decisions[test_anomalies] = 1  # 初步异常点
    reasoning_decisions[filtered_anomalies] = 2  # 最终异常点
    reasoning_decisions[normal_indices] = 3  # 规则排除点
    
    # 1. 绘制知识推理决策完整曲线
    plt.figure(figsize=(12.8, 7.0))
    
    # 设置白色背景
    plt.gca().set_facecolor('white')
    plt.gcf().set_facecolor('white')
    
    # 绘制张力曲线
    plt.plot(tension_actual, color='#1f77b4', linewidth=2, label='张力真实值')
    
    # 标记不同类型的点，使用专业配色
    if len(test_anomalies) > 0:
        plt.scatter(test_anomalies, tension_actual[test_anomalies], 
                   color='#ff7f0e', s=100, label='初步异常点', marker='^')
    
    if len(filtered_anomalies) > 0:
        plt.scatter(filtered_anomalies, tension_actual[filtered_anomalies], 
                   color='#d62728', s=120, label='最终异常点', marker='*')
    
    if len(normal_indices) > 0:
        plt.scatter(normal_indices, tension_actual[normal_indices], 
                   color='#2ca02c', s=80, label='规则排除点', marker='x')
    
    # 添加标题和标签
    plt.title('知识推理引擎决策曲线分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间序列', fontsize=14, labelpad=10)
    plt.ylabel('张力值 (KN)', fontsize=14, labelpad=10)
    
    # 优化图例
    plt.legend(fontsize=12, loc='upper right', framealpha=0.9, 
              edgecolor='none', fancybox=True)
    
    # 优化网格
    plt.grid(True, linestyle='--', alpha=0.3, color='gray')
    
    # 优化刻度标签
    plt.tick_params(axis='both', which='major', labelsize=12)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('knowledge_reasoning_decisions_tension.png', 
                dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    # 2. 绘制三引擎协同决策分析
    plt.figure(figsize=(12.8, 7.0))
    
    # 分析每个异常点的引擎决策情况
    engine_decisions = {
        'physics_valid': [],
        'case_valid': [],
        'rule_valid': [],
        'final_decision': []
    }
    
    # 重新运行知识推理以收集详细决策信息
    for idx in test_anomalies:
        current_value = tension_actual[idx]
        vibration_value = vibration_actual[idx]
        
        # 计算变化量
        if idx > 0:
            tension_change = tension_actual[idx] - tension_actual[idx-1]
            vibration_change = vibration_actual[idx] - vibration_actual[idx-1]
        else:
            tension_change = 0
            vibration_change = 0
        
        freq_dominant = freq_features_test[idx][3]
        
        # 规则引擎决策（简化版）
        rule_valid = True
        if idx in [78, 427, 809]:  # 工况转换点
            rule_valid = False
        elif abs(tension_change) < 0.5 and abs(vibration_change) < 0.5:  # 微小波动
            rule_valid = False
        
        # 物理引擎决策
        physics_valid = physics_engine.validate_anomaly(
            idx, current_value, vibration_value,
            tension_change, vibration_change, freq_features_test[idx]
        )
        
        # 案例引擎决策
        case_valid = case_engine.validate_anomaly(
            current_value, vibration_value, tension_change, freq_dominant
        )
        
        # 最终决策
        final_decision = idx in filtered_anomalies
        
        engine_decisions['physics_valid'].append(physics_valid)
        engine_decisions['case_valid'].append(case_valid)
        engine_decisions['rule_valid'].append(rule_valid)
        engine_decisions['final_decision'].append(final_decision)
    
    # 绘制引擎决策统计
    engine_names = ['规则引擎', '物理引擎', '案例引擎', '最终决策']
    engine_counts = [
        sum(engine_decisions['rule_valid']),
        sum(engine_decisions['physics_valid']),
        sum(engine_decisions['case_valid']),
        sum(engine_decisions['final_decision'])
    ]
    
    # 设置专业配色方案
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#9467bd']  # 蓝、橙、绿、紫
    
    # 创建渐变效果
    for i, (name, count, color) in enumerate(zip(engine_names, engine_counts, colors)):
        bar = plt.bar(i, count, color=color, alpha=0.8)
        
        # 添加数值标签
        plt.text(i, count + 0.5, str(count),
                ha='center', va='bottom',
                fontsize=12, fontweight='bold',
                color=color)
    
    # 设置白色背景
    plt.gca().set_facecolor('white')
    plt.gcf().set_facecolor('white')
    
    # 优化标题和标签
    plt.title('三引擎协同决策统计分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('推理引擎', fontsize=14, labelpad=10)
    plt.ylabel('确认异常点数量', fontsize=14, labelpad=10)
    
    # 优化刻度
    plt.xticks(range(len(engine_names)), engine_names, fontsize=12)
    plt.tick_params(axis='both', which='major', labelsize=12)
    
    # 优化网格
    plt.grid(True, linestyle='--', alpha=0.3, axis='y', color='gray')
    
    # 添加总异常点数量信息，使用更专业的样式
    info_text = (f'统计信息:\n'
                f'• 初步异常点: {len(test_anomalies)}个\n'
                f'• 最终异常点: {len(filtered_anomalies)}个\n'
                f'• 排除误报: {len(normal_indices)}个')
    
    plt.text(0.02, 0.98, info_text,
             transform=plt.gca().transAxes,
             fontsize=11,
             verticalalignment='top',
             bbox=dict(boxstyle="round,pad=0.5",
                      facecolor='white',
                      edgecolor='gray',
                      alpha=0.9))
    
    # 调整布局
    plt.tight_layout()
    
    # 保存高质量图片
    plt.savefig('three_engines_decision_statistics.png',
                dpi=300,
                bbox_inches='tight',
                facecolor='white')
    plt.show()
    
    # 3. 绘制引擎决策一致性分析
    plt.figure(figsize=(12.8, 7.0))
    
    # 设置白色背景
    plt.gca().set_facecolor('white')
    plt.gcf().set_facecolor('white')
    
    # 计算引擎间的一致性
    consistency_matrix = np.zeros((3, 3))  # 规则、物理、案例引擎
    engine_results = [
        engine_decisions['rule_valid'],
        engine_decisions['physics_valid'],
        engine_decisions['case_valid']
    ]
    
    for i in range(3):
        for j in range(3):
            if i != j:
                # 计算两个引擎决策的一致性
                agreements = sum(a == b for a, b in zip(engine_results[i], engine_results[j]))
                consistency_matrix[i, j] = agreements / len(engine_results[i]) * 100 if len(engine_results[i]) > 0 else 0
    
    # 创建1280x700分辨率的图形
    plt.figure(figsize=(12.8, 7.0))
    
    # 创建主坐标轴并设置位置
    ax = plt.gca()
    ax.set_position([0.2, 0.15, 0.5, 0.7])  # 左边距20%，底部15%，宽度50%，高度70%
    
    # 使用专业的颜色方案
    cmap = plt.cm.get_cmap('RdYlBu_r')  # 红-黄-蓝配色，反转以使红色表示高一致性
    
    # 绘制一致性热力图
    engine_labels = ['规则引擎', '物理引擎', '案例引擎']
    im = ax.imshow(consistency_matrix, cmap=cmap, aspect='equal', vmin=0, vmax=100)
    
    # 添加数值标签
    for i in range(3):
        for j in range(3):
            if i != j:
                value = consistency_matrix[i, j]
                color = 'white' if value > 50 else 'black'
                ax.text(j, i, f'{value:.1f}%',
                       ha='center', va='center',
                       fontsize=13, fontweight='bold',
                       color=color)
            else:
                ax.text(j, i, ' ',
                       ha='center', va='center',
                       fontsize=13, fontweight='bold',
                       color='gray')
    
    # 添加颜色条
    cbar_ax = plt.axes([0.75, 0.15, 0.02, 0.7])  # 左边距75%，底部15%，宽度2%，高度70%
    cbar = plt.colorbar(im, cax=cbar_ax)
    cbar.set_label('一致性百分比 (%)', fontsize=12, labelpad=10)
    cbar.ax.tick_params(labelsize=10)
    
    # 设置坐标轴标签
    ax.set_xticks(range(3))
    ax.set_yticks(range(3))
    ax.set_xticklabels(engine_labels, fontsize=12)
    ax.set_yticklabels(engine_labels, fontsize=12)
    
    # 添加标题
    plt.suptitle('知识推理引擎决策一致性矩阵', fontsize=16, fontweight='bold', y=0.95)
    
    # 保存高质量图片到知识推理文件夹
    save_path = os.path.join(plot_dirs['knowledge'], 'engine_decision_consistency.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    # 4. 绘制异常点类型分布时序图
    plt.figure(figsize=(12.8, 7.0))
    
    # 创建时序决策图
    time_steps = range(len(reasoning_decisions))
    
    # 绘制不同类型点的时序分布
    normal_mask = (reasoning_decisions == 0)
    initial_anomaly_mask = (reasoning_decisions == 1)
    final_anomaly_mask = (reasoning_decisions == 2)
    excluded_mask = (reasoning_decisions == 3)
    
    plt.scatter(np.where(normal_mask)[0], np.zeros(np.sum(normal_mask)), 
               color='lightblue', s=20, alpha=0.6, label='正常点')
    plt.scatter(np.where(initial_anomaly_mask)[0], np.ones(np.sum(initial_anomaly_mask)), 
               color='yellow', s=40, alpha=0.8, label='初步异常点')
    plt.scatter(np.where(final_anomaly_mask)[0], np.full(np.sum(final_anomaly_mask), 2), 
               color='red', s=60, alpha=0.9, label='最终异常点')
    plt.scatter(np.where(excluded_mask)[0], np.full(np.sum(excluded_mask), 3), 
               color='green', s=40, alpha=0.8, label='规则排除点')
    
    plt.title('知识推理决策时序分布图', fontsize=14, fontweight='bold')
    plt.xlabel('时间序列', fontsize=12)
    plt.ylabel('决策类型', fontsize=12)
    plt.yticks([0, 1, 2, 3], ['正常', '初步异常', '最终异常', '规则排除'])
    plt.legend(fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    # plt.savefig('reasoning_decisions_timeline.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 5. 绘制物理引擎验证历史分析
    if len(physics_engine.history) > 0:
        plt.figure(figsize=(12.8, 7.0))
        
        # 分析物理引擎验证的原因分布
        reasons = [item[1] for item in physics_engine.history]
        reason_counts = {}
        for reason in reasons:
            reason_counts[reason] = reason_counts.get(reason, 0) + 1
        
        # if reason_counts:
        #     plt.pie(reason_counts.values(), labels=reason_counts.keys(), autopct='%1.1f%%',
        #            startangle=90, colors=['lightcoral', 'lightskyblue', 'lightgreen', 'gold'])
        #     plt.title('物理引擎验证原因分布', fontsize=14, fontweight='bold')
        #     plt.axis('equal')
        #     plt.tight_layout()
        #     # plt.savefig('physics_engine_validation_reasons.png', dpi=300, bbox_inches='tight')
        #     plt.show()
    
    # 6. 输出统计信息
    print("\n=== 知识推理引擎统计信息 ===")
    print(f"初步异常点数量: {len(test_anomalies)}")
    print(f"最终确认异常点数量: {len(filtered_anomalies)}")
    print(f"规则排除点数量: {len(normal_indices)}")
    print(f"异常点确认率: {len(filtered_anomalies)/len(test_anomalies)*100:.2f}%" if len(test_anomalies) > 0 else "异常点确认率: 0%")
    print(f"误报排除率: {len(normal_indices)/len(test_anomalies)*100:.2f}%" if len(test_anomalies) > 0 else "误报排除率: 0%")
    
    if len(test_anomalies) > 0:
        print(f"\n=== 三引擎决策统计 ===")
        print(f"规则引擎确认率: {sum(engine_decisions['rule_valid'])/len(test_anomalies)*100:.2f}%")
        print(f"物理引擎确认率: {sum(engine_decisions['physics_valid'])/len(test_anomalies)*100:.2f}%")
        print(f"案例引擎确认率: {sum(engine_decisions['case_valid'])/len(test_anomalies)*100:.2f}%")
    
    if len(physics_engine.history) > 0:
        print(f"\n=== 物理引擎验证历史 ===")
        for reason, count in reason_counts.items():
            print(f"{reason}: {count}次")
    
    return {
        'reasoning_decisions': reasoning_decisions,
        'engine_decisions': engine_decisions,
        'consistency_matrix': consistency_matrix,
        'statistics': {
            'initial_anomalies': len(test_anomalies),
            'final_anomalies': len(filtered_anomalies),
            'excluded_points': len(normal_indices)
        }
    }

# 绘制知识推理引擎完整输出曲线
print("绘制知识推理引擎完整输出曲线...")
knowledge_reasoning_results = plot_knowledge_reasoning_outputs(
    test_anomalies, filtered_anomalies, normal_indices,
    tension_actual, vibration_actual, freq_features_test,
    physics_engine, case_engine, test_z_scores, plot_dirs
)



# 模型评估
tension_rmse = np.sqrt(mean_squared_error(tension_actual, tension_pred))
tension_mae = mean_absolute_error(tension_actual, tension_pred)

vibration_rmse = np.sqrt(mean_squared_error(vibration_actual, vibration_pred))
vibration_mae = mean_absolute_error(vibration_actual, vibration_pred)

print(f"张力测试集RMSE: {tension_rmse:.4f}, MAE: {tension_mae:.4f}")
print(f"振动测试集RMSE: {vibration_rmse:.4f}, MAE: {vibration_mae:.4f}")
print(f"初步异常点数量: {len(test_anomalies)}/{len(tension_pred)} ({len(test_anomalies)/len(tension_pred)*100:.2f}%)")
print(f"异常点数: {len(filtered_anomalies)}/{len(tension_pred)} ({len(filtered_anomalies)/len(tension_pred)*100:.2f}%)")
print(f"误报点数: {len(normal_indices)}")
print(f"动态阈值: {dynamic_threshold:.4f}")


# 9. 异常检测结果可视化 -----------------------------------------------------------

# 张力预测结果对比
plt.figure(figsize=(12.8, 7.0))
plt.plot(tension_actual, label='张力真实值', color='blue', alpha=0.7, linewidth=2)
plt.plot(tension_pred, label='张力预测值', color='orange', alpha=0.7, linestyle='--', linewidth=2)

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, tension_actual[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title('TCN-xLSTM+知识推理 测试集张力预测结果', fontsize=14, fontweight='bold')
plt.xlabel('时间序列', fontsize=12)
plt.ylabel('张力值 (KN)', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(tension_actual), 80))
# 保存到知识推理文件夹
save_path = os.path.join(plot_dirs['knowledge'], 'tension_prediction_result.png')
plt.savefig(save_path, dpi=300, bbox_inches='tight')
plt.show()

# 振动预测结果对比
plt.figure(figsize=(12.8, 7.0))
plt.plot(vibration_actual, label='振动真实值', color='green', alpha=0.7, linewidth=2)
plt.plot(vibration_pred, label='振动预测值', color='purple', alpha=0.7, linestyle='--', linewidth=2)

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, vibration_actual[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title('TCN-xLSTM+知识推理 测试集振动预测结果', fontsize=14, fontweight='bold')
plt.xlabel('时间序列', fontsize=12)
plt.ylabel('振动值 (G)', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(vibration_actual), 80))
# 保存到知识推理文件夹
save_path = os.path.join(plot_dirs['knowledge'], 'vibration_prediction_result.png')
plt.savefig(save_path, dpi=300, bbox_inches='tight')
plt.show()

# 融合误差和Z-score可视化
plt.figure(figsize=(12.8, 7.0))
plt.plot(fused_errors, color='blue', alpha=0.6, label='融合误差', linewidth=2)
plt.plot(test_z_scores, color='green', label='Z-score', linewidth=2)
plt.axhline(y=1.27, color='red', linestyle='--', linewidth=2, label='异常阈值 (Z=1.27)')
plt.axhline(y=dynamic_threshold, color='purple', linestyle='-.', linewidth=2, label=f'动态阈值 ({dynamic_threshold:.2f})')

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, test_z_scores[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title('TCN-xLSTM+知识推理 多物理量融合异常检测', fontsize=14, fontweight='bold')
plt.xlabel('时间序列', fontsize=12)
plt.ylabel('融合误差/Z-score', fontsize=12)
plt.legend(fontsize=10)
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(fused_errors), 80))
# 保存到知识推理文件夹
save_path = os.path.join(plot_dirs['knowledge'], 'anomaly_detection_result.png')
plt.savefig(save_path, dpi=300, bbox_inches='tight')
plt.show()

# 10. 异常点分布热力图 -----------------------------------------------------------
plt.figure(figsize=(12.8, 7.0))
anomaly_mask = np.zeros(len(tension_actual))
anomaly_mask[filtered_anomalies] = 1

# 规则引擎排除点标记为2
if len(normal_indices) > 0:
    anomaly_mask[normal_indices] = 2

# 创建包含两个特征和异常标记的DataFrame
anomaly_df = pd.DataFrame({
    'Tension': tension_actual,
    'Vibration': vibration_actual,
    'Anomaly': anomaly_mask
})

# 绘制异常点分布
colors = ['blue', 'red', 'green']  # 0=正常, 1=异常, 2=规则排除
scatter = plt.scatter(anomaly_df['Tension'], anomaly_df['Vibration'], 
                     c=anomaly_df['Anomaly'], cmap=plt.cm.colors.ListedColormap(colors), 
                     alpha=0.7, s=40)
plt.colorbar(scatter, ticks=[0, 1, 2], label='点类型 (0=正常, 1=异常, 2=规则排除)')
plt.title('张力-振动特征空间中的异常点分布', fontsize=14, fontweight='bold')
plt.xlabel('张力值 (KN)', fontsize=12)
plt.ylabel('振动值 (G)', fontsize=12)
plt.grid(True, linestyle='--', alpha=0.3)

# 添加密度等高线
try:
    sns.kdeplot(
        x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==0],
        fill=True, thresh=0.05, alpha=0.2, levels=5, cmap="Blues"
    )
    sns.kdeplot(
        x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==1],
        fill=True, thresh=0.01, alpha=0.2, levels=5, cmap="Reds"
    )
    sns.kdeplot(
        x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==2],
        fill=True, thresh=0.01, alpha=0.2, levels=5, cmap="Greens"
    )
except:
    print("密度等高线绘制失败，跳过...")

plt.tight_layout()
# plt.savefig('anomaly_distribution_heatmap.png', dpi=300, bbox_inches='tight')
plt.show()

# 11. 特征重要性分析（基于模型权重） -----------------------------------------------------------
def calculate_feature_importance(model, feature_names):
    # 获取TCN输入投影层的权重
    tcn_weights = model.tcn.input_proj.weight.data.cpu().numpy()
    
    # 计算每个特征的平均绝对权重
    avg_weights = np.mean(np.abs(tcn_weights), axis=0)
    
    # 归一化
    total = np.sum(avg_weights)
    normalized_weights = avg_weights / total
    
    # 创建特征重要性字典
    importance_dict = {}
    for i, name in enumerate(feature_names):
        importance_dict[name] = float(normalized_weights[i])  # 确保转换为标量
    
    # 添加卡尔曼滤波的贡献
    kalman_features = [name for name in feature_names if 'kalman' in name or 'var' in name]
    kalman_importance = sum([importance_dict[name] for name in kalman_features])
    importance_dict['Kalman_Total'] = kalman_importance
    
    # 添加频域特征的贡献
    freq_features = [name for name in feature_names if 'freq' in name]
    freq_importance = sum([importance_dict[name] for name in freq_features])
    importance_dict['Frequency_Total'] = freq_importance
    
    return importance_dict


# 计算特征重要性
feature_importance = calculate_feature_importance(model, features)

# 可视化特征重要性
plt.figure(figsize=(12.8, 7.0))
sorted_importance = dict(sorted(feature_importance.items(), key=lambda item: item[1], reverse=True))

# 确保所有值都是标量
feature_names = list(sorted_importance.keys())
feature_values = [float(v) for v in sorted_importance.values()]

# 使用不同颜色区分特征类型
colors = []
for name in feature_names:
    if 'kalman' in name.lower() or 'var' in name.lower():
        colors.append('orange')  # 卡尔曼特征
    elif 'freq' in name.lower():
        colors.append('green')   # 频域特征
    elif name in ['Kalman_Total']:
        colors.append('red')     # 卡尔曼总计
    elif name in ['Frequency_Total']:
        colors.append('purple')  # 频域总计
    else:
        colors.append('skyblue') # 基础特征

plt.bar(feature_names, feature_values, color=colors, alpha=0.8)
plt.title('特征在异常检测中的重要性分析', fontsize=14, fontweight='bold')
plt.xlabel('特征名称', fontsize=12)
plt.ylabel('相对重要性', fontsize=12)
plt.xticks(rotation=45, ha='right', fontsize=10)
plt.grid(True, linestyle='--', alpha=0.3, axis='y')
plt.tight_layout()
# plt.savefig('feature_importance_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

print("TCN-xLSTM融合异常检测模型训练完成！")